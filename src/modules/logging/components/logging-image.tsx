import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useGetListAttributeBySuiteId } from "@/modules/downhole-point/hooks/useGetAttributeBySuiteId";
import { useGetDownholeByProject } from "@/modules/downhole-point/hooks/useGetDownholeByProject";
import { getDetailDrillhole } from "@/modules/drillhole/redux/drillholeSlice/thunks";
import { getGeologySuite } from "@/modules/geology-suite/redux/thunks";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import imageRequest from "@/modules/image/api/image.api";
import { resizeLoggingPointData } from "@/modules/image/helpers/image.helpers";
import { ModalOcrList } from "@/modules/process-images/components/ocr/modal-ocr-list";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import { Button, Select, Tag, TreeSelect } from "antd";
import { isEmpty, uniqueId } from "lodash";
import { useSearchParams } from "next/navigation";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import recoveryRequest from "../api/recovery.api";
import { LoggingViewStack } from "../model/dtos/logging.config";
import {
  EnumLoggingExtraViews,
  EnumLoggingViewStack,
} from "../model/enum/logging.enum";
import {
  setImageHyperRows,
  updateExtraviewImageSubtype,
  updateExtraviewImageType,
  updateExtraViews,
  updateMeasurePointsInterval,
  updateRecoveries,
  updateSelectedDrilhole,
  updateSelectedRockLineType,
} from "../redux/loggingSlice";
import { getExtraViewModeImages } from "../redux/loggingSlice/thunks";
import {
  OCRResultItem,
  RockLineType,
  ImageRow,
  ImageTypeTreeNode,
  TypeSubTypeIds,
  LoggingImageProps,
  GeophysicsPoint,
} from "../types/logging.types";
import LoggingStage from "./logging-stage";

const viewsOptions = [
  {
    label: "Below",
    value: EnumLoggingExtraViews.Below,
  },
  {
    label: "Overlay",
    value: EnumLoggingExtraViews.Overlay,
  },
];

const rockLineOptions = [
  {
    label: "Recovery",
    value: RockLineType.Recovery,
  },
  {
    label: "RQD",
    value: RockLineType.RQD,
  },
];

/**
 * LoggingImage component for displaying and managing logging images with various view modes
 * and interactive features like OCR, geophysics data, and rock line visualization.
 */
function LoggingImage({
  imageRows,
  viewModes,
  setViewModes,
  directOCRdata,
  setDirectOCRdata,
  image,
  directOCRdataRaw,
  getCurrentImage,
  setCurrentImage,
  selectedImageType,
  selectedImageSubtype,
}: LoggingImageProps) {
  const dispatch = useAppDispatch();
  const queries: Record<string, string | string[]> = {};
  const searchParams = useSearchParams();

  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const params = new URLSearchParams(queries as Record<string, string>);

  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const structure = useAppSelector((state) => state.structure?.detail);

  const imageDepthFrom = useAppSelector((state) => state.logging.depthFrom);
  const imageDepthTo = useAppSelector((state) => state.logging.depthTo);
  const projectDetail = useAppSelector((state) => state.project.detail);
  const skipCount = useAppSelector((state) => state.logging.skipCount);

  const selectedRockLineType = useAppSelector(
    (state) => state.logging.selectedRockLineType
  );
  const loggingSuiteMode = useAppSelector(
    (state) => state.logging.loggingSuiteMode
  );
  const measurePointsInterval = useAppSelector(
    (state) => state.logging.measurePointsInterval
  );
  const imageExtraRows = useAppSelector(
    (state) => state?.logging?.imageExtraRows
  );
  const imageGap = useAppSelector((state) => state.logging.imageGap);

  const extraViews = useAppSelector((state) => state?.logging?.extraViews);
  const selectedDrillHole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation
  );
  const isInterval = structure?.selector === StructureSelectorType.Interval;

  const [selectedAttribute, setSelectedAttribute] = useState<string[]>();
  const [selectedGeophysic, setSelectedGeophysic] = useState<string>();
  const [isOpenModalOCRList, setIsOpenModalOCRList] = useState<boolean>(false);
  const [OCRdata, setOCRdata] = useState<OCRResultItem[]>([]);
  const [imageTypeValue, setImageTypeValue] = useState<string | undefined>();

  const { data: imageTypeData, isLoading: isFetchingImageType } =
    useQueryImageType();

  /**
   * Transforms image type data into tree node structure for TreeSelect component
   */
  const transformDataToTreeNodes = useCallback(
    (data: any[] | undefined): ImageTypeTreeNode[] => {
      if (!data) return [];
      return data.map((type: any) => ({
        title: type.name,
        value: `type-${type.id}`,
        key: `type-${type.id}`,
        children: (type.imageSubtypes || []).map((subtype: any) => ({
          title: subtype.name,
          value: `subtype-${subtype.id}-type-${type.id}`,
          key: `subtype-${subtype.id}-type-${type.id}`,
        })),
      }));
    },
    []
  );

  const imageTypeTreeData = useMemo(
    () => transformDataToTreeNodes(imageTypeData?.data?.items),
    [imageTypeData, transformDataToTreeNodes]
  );

  /**
   * Converts tree node value to type and subtype IDs
   */
  const convertToTypeSubTypeId = useCallback(
    (treeNodeValue: string): TypeSubTypeIds => {
      let typeId: number | undefined = undefined;
      let subTypeId: number | undefined = undefined;
      if (treeNodeValue) {
        if (treeNodeValue.startsWith("subtype-")) {
          const parts = treeNodeValue.split("-");
          subTypeId = Number(parts[1]);
          typeId = Number(parts[3]);
        } else if (treeNodeValue.startsWith("type-")) {
          const parts = treeNodeValue.split("-");
          typeId = Number(parts[1]);
        }
      }
      return {
        typeId,
        subTypeId,
      };
    },
    []
  );

  /**
   * Handles image type change and updates extra view images if needed
   */
  const handleImageTypeChange = useCallback(
    (value: string) => {
      setImageTypeValue(value);
      const { typeId, subTypeId } = convertToTypeSubTypeId(value);

      dispatch(updateExtraviewImageType(typeId));
      dispatch(updateExtraviewImageSubtype(subTypeId));

      const shouldFetchExtraImages =
        value &&
        (viewModes.includes(EnumLoggingExtraViews.Below) ||
          viewModes.includes(EnumLoggingExtraViews.Overlay));

      if (shouldFetchExtraImages) {
        dispatch(
          getExtraViewModeImages({
            imageSubtypeId: subTypeId,
            imageTypeId: typeId,
            projectIds: [globalProjectId],
            prospectIds: [globalProspectId],
            holeIds: [selectedDrillhole?.value],
            depthFrom: imageDepthFrom,
            depthTo: parseFloat(imageDepthTo.toFixed(2)),
          })
        );
        dispatch(updateExtraViews(viewModes as EnumLoggingExtraViews[]));
      } else {
        dispatch(updateExtraViews([]));
        dispatch(setImageHyperRows([]));
      }
    },
    [
      convertToTypeSubTypeId,
      viewModes,
      dispatch,
      globalProjectId,
      globalProspectId,
      selectedDrillhole?.value,
      imageDepthFrom,
      imageDepthTo,
    ]
  );

  /**
   * Handles view mode changes and fetches extra view images if needed
   */
  const onChangeViewModes = useCallback(
    (selections: EnumLoggingExtraViews[]) => {
      setViewModes(selections);

      if (!imageTypeValue) return;

      const shouldFetchExtraImages =
        imageTypeValue &&
        (selections.includes(EnumLoggingExtraViews.Below) ||
          selections.includes(EnumLoggingExtraViews.Overlay));

      if (!shouldFetchExtraImages) {
        dispatch(updateExtraViews([]));
        dispatch(setImageHyperRows([]));
        return;
      }

      const { typeId, subTypeId } = convertToTypeSubTypeId(imageTypeValue);

      dispatch(
        getExtraViewModeImages({
          imageSubtypeId: subTypeId,
          imageTypeId: typeId,
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
          holeIds: [selectedDrillhole?.value],
          depthFrom: imageDepthFrom,
          depthTo: parseFloat(imageDepthTo.toFixed(2)),
        })
      );
      dispatch(updateExtraViews(selections as EnumLoggingExtraViews[]));
    },
    [
      imageTypeValue,
      convertToTypeSubTypeId,
      dispatch,
      globalProjectId,
      globalProspectId,
      selectedDrillhole?.value,
      imageDepthFrom,
      imageDepthTo,
    ]
  );

  const drillholeIdParams = params.get("drillholeId");

  const {
    data: geophysicsAttributes,
    request: requestGetListAttributeBySuiteId,
  } = useGetListAttributeBySuiteId();

  /**
   * Handles attribute selection for geophysics data
   */
  const handleSelectAttributes = useCallback((value: string[]) => {
    setSelectedAttribute(value);
  }, []);

  /**
   * Handles rock line type change
   */
  const handleRockLineTypeChange = useCallback(
    (value: RockLineType) => {
      dispatch(updateSelectedRockLineType(value));
    },
    [dispatch]
  );

  /**
   * Handles geophysics suite selection and fetches related attributes
   */
  const handleSelectGeophysics = useCallback(
    (value: string) => {
      setSelectedGeophysic(value);
      if (value) {
        requestGetListAttributeBySuiteId({
          Id: value,
        });
      } else {
        setSelectedAttribute(undefined);
      }
    },
    [requestGetListAttributeBySuiteId]
  );

  const { request: requestGetDownholeByProject, data: dataDownholes } =
    useGetDownholeByProject();

  /**
   * Updates OCR data via API call
   */
  const updateOCRDataAPI = useCallback(
    async (updatedData: OCRResultItem[]): Promise<void> => {
      try {
        await imageRequest.updateResultOCR({
          id: image?.id,
          ocr: JSON.stringify(updatedData),
        });
        toast.success("Update OCR success", {
          position: "top-center",
        });
      } catch (error) {
        toast.error("Update OCR failed", {
          position: "top-center",
        });
        throw error;
      }
    },
    [image?.id]
  );

  /**
   * Handles OCR text change on enter key press
   */
  const onEnterChangeText = useCallback(
    (data: OCRResultItem, value: string) => {
      const dataUpdate = directOCRdata.map((item) => {
        if (item.id === data.id) {
          return {
            ...item,
            text: value,
          };
        }
        return item;
      });
      setDirectOCRdata(dataUpdate);
      updateOCRDataAPI(dataUpdate);
    },
    [directOCRdata, updateOCRDataAPI]
  );

  /**
   * Handles OCR deletion
   */
  const onDeleteOcr = useCallback(
    (id: string) => {
      const dataUpdate = directOCRdata.filter((item) => item.id !== id);
      updateOCRDataAPI(dataUpdate)
        .then(() => {
          setDirectOCRdata(dataUpdate);
        })
        .catch(() => {
          // Error already handled in updateOCRDataAPI
        });
    },
    [directOCRdata, updateOCRDataAPI]
  );

  /**
   * Handles double click on OCR text to enable editing
   */
  const onDblClickText = useCallback(
    (e: any) => {
      const updatedData = directOCRdata.map((item) => {
        if (item.id === e.id) {
          return {
            ...item,
            isEdit: true,
          };
        }
        return {
          ...item,
          isEdit: false,
        };
      });
      setDirectOCRdata(updatedData);
    },
    [directOCRdata]
  );

  /**
   * Handles OCR annotation changes
   */
  const onChange = useCallback(
    (newAttrs: any) => {
      const newAnnotations = directOCRdata.map((annotation: OCRResultItem) => {
        if (annotation.id === newAttrs.id) {
          return newAttrs;
        }
        return annotation;
      });
      setDirectOCRdata(newAnnotations);
    },
    [directOCRdata]
  );

  /**
   * Handles OCR text changes during editing
   */
  const onChangeText = useCallback(
    (data: OCRResultItem, value: string) => {
      const updatedData = directOCRdata.map((item) => {
        if (item.id === data.id) {
          return {
            ...item,
            draftText: Number(value),
          };
        }
        return item;
      });
      setDirectOCRdata(updatedData);
    },
    [directOCRdata]
  );

  /**
   * Refreshes image data by fetching the current image
   */
  const refreshImageData = useCallback(() => {
    const drillholeId = drillholeIdParams ?? selectedDrillHole?.value;
    const imageSkipCount = skipCount ?? 0;
    if (drillholeId) {
      dispatch(
        updateSelectedDrilhole({
          value: selectedDrillhole?.value,
          label: selectedDrillhole?.label,
        })
      );
      getCurrentImage(
        String(drillholeId),
        imageSkipCount + 1,
        selectedImageType,
        selectedImageSubtype
      );
      setCurrentImage(imageSkipCount + 1);
      dispatch(getDetailDrillhole(Number(drillholeId)));
    }
  }, [
    drillholeIdParams,
    selectedDrillHole?.value,
    skipCount,
    dispatch,
    selectedDrillhole?.value,
    selectedDrillhole?.label,
    getCurrentImage,
    selectedImageType,
    selectedImageSubtype,
    setCurrentImage,
  ]);

  /**
   * Calculates the relative start X position for an image row
   */
  const calculateRelativeStartX = useCallback(
    (imageRows: ImageRow[], index: number): number => {
      if (index === 0) return 0;

      let relativeStartX = 0;
      for (let j = 0; j < index; j++) {
        relativeStartX += imageRows[j].coordinate.Width;
      }
      return relativeStartX;
    },
    []
  );

  /**
   * Creates an image stack for the main image
   */
  const createImageStack = useCallback(
    (
      imageRow: ImageRow,
      index: number,
      startY: number,
      relativeStartX: number
    ): LoggingViewStack => {
      return {
        id: `${imageRow.id}-${EnumLoggingViewStack.Image}`,
        data: {
          src: imageRow.urlCroppedImage,
          width: imageRow.coordinate.Width,
          height: imageRow.coordinate.Height,
          depthFrom: imageRow.depthFrom,
          depthTo: imageRow.depthTo,
          isShowOCR: true,
          isShowText: true,
          id: imageRow.id,
          x: imageRow.coordinate.X,
          y: imageRow.coordinate.Y,
          relativeStartX: relativeStartX,
          relativeEndX: relativeStartX + imageRow.coordinate.Width,
          rockLines: imageRow.rockLines,
        },
        startY: startY + imageGap,
        type: EnumLoggingViewStack.Image,
        index: index,
      };
    },
    [imageGap]
  );

  /**
   * Creates overlay image stack if conditions are met
   */
  const createOverlayStack = useCallback(
    (
      imageRow: ImageRow,
      extraImageRow: any,
      index: number,
      startY: number
    ): LoggingViewStack | null => {
      if (
        !imageExtraRows?.length ||
        isEmpty(extraImageRow) ||
        !extraViews.includes(EnumLoggingExtraViews.Overlay)
      ) {
        return null;
      }

      return {
        id: `${extraImageRow.id}-${EnumLoggingViewStack.Image}-${EnumLoggingExtraViews.Overlay}`,
        data: {
          src: extraImageRow.urlCroppedImage,
          width: imageRow.coordinate.Width,
          height:
            extraImageRow.coordinate.Height *
            (imageRow.coordinate.Width / extraImageRow.coordinate.Width),
          depthFrom: extraImageRow.depthFrom,
          depthTo: extraImageRow.depthTo,
          isShowOCR: false,
          isShowText: false,
          id: imageRow.id,
        },
        startY: startY,
        type: EnumLoggingViewStack.Overlay,
        index: index,
        uiProps: {
          opacity: 0.6,
        },
      };
    },
    [imageExtraRows, extraViews]
  );

  /**
   * Creates below image stack if conditions are met
   */
  const createBelowStack = useCallback(
    (
      imageRow: ImageRow,
      extraImageRow: any,
      index: number,
      startY: number,
      previousHeight: number
    ): LoggingViewStack | null => {
      if (
        !imageExtraRows?.length ||
        isEmpty(extraImageRow) ||
        !extraViews.includes(EnumLoggingExtraViews.Below)
      ) {
        return null;
      }

      return {
        id: `${extraImageRow.id}-${EnumLoggingViewStack.Image}-${EnumLoggingExtraViews.Below}`,
        data: {
          src: extraImageRow.urlCroppedImage,
          width: imageRow.coordinate.Width,
          height:
            extraImageRow.coordinate.Height *
            (imageRow.coordinate.Width / extraImageRow.coordinate.Width),
          depthFrom: extraImageRow.depthFrom,
          depthTo: extraImageRow.depthTo,
          isShowOCR: false,
          isShowText: false,
          id: imageRow.id,
        },
        startY: startY + previousHeight,
        type: EnumLoggingViewStack.Below,
        index: index,
      };
    },
    [imageExtraRows, extraViews]
  );

  /**
   * Creates point data stacks for geophysics visualization
   */
  const createPointStacks = useCallback(
    (
      imageRow: ImageRow,
      startY: number,
      previousHeight: number
    ): LoggingViewStack[] => {
      if (!selectedAttribute?.length || !dataDownholes) {
        return [];
      }

      return (selectedAttribute ?? [])
        .map((geophysicName, pointIndex) => {
          const geophysicDatas = dataDownholes
            ?.filter(
              (dataDownhole: any) =>
                Number(dataDownhole["Depth (m)"]) >=
                  Number(imageRow.depthFrom) &&
                Number(dataDownhole["Depth (m)"]) <= Number(imageRow.depthTo)
            )
            .map((dataDownhole: any) => ({
              x: dataDownhole["Depth (m)"],
              y: dataDownhole[geophysicName],
            }));

          // Filter to get unique data, always get latest value
          const uniqueGeophysicData = [
            ...new Map(
              geophysicDatas.map((item: GeophysicsPoint) => [item.x, item])
            ).values(),
          ];
          uniqueGeophysicData.sort((a, b) => parseFloat(a.x) - parseFloat(b.x));

          if (uniqueGeophysicData.length === 0) return null;

          const resizePoints = resizeLoggingPointData(
            uniqueGeophysicData,
            imageRow.depthFrom,
            imageRow.depthTo,
            imageRow.coordinate.Width,
            120
          );

          return {
            id: uniqueId(),
            type: EnumLoggingViewStack.Point,
            data: resizePoints,
            index: pointIndex,
            startY: startY + previousHeight + 300,
          };
        })
        .filter((item): item is LoggingViewStack => item !== null);
    },
    [selectedAttribute, dataDownholes]
  );

  const loggingViewStacks = useMemo(() => {
    const stacks: LoggingViewStack[] = [];
    let previousStartY = 200;
    let previousHeight = 0;

    for (let i = 0; i < imageRows.length; i++) {
      const relativeStartX = calculateRelativeStartX(imageRows, i);

      // Add main image stack
      const imageStack = createImageStack(
        imageRows[i],
        i,
        previousStartY + previousHeight,
        relativeStartX
      );

      previousStartY = imageStack.startY;
      previousHeight = imageStack.data.height;
      stacks.push(imageStack);

      // Add overlay stack if needed
      const overlayStack = createOverlayStack(
        imageRows[i],
        imageExtraRows?.[i],
        i,
        previousStartY
      );

      if (overlayStack) {
        previousStartY = overlayStack.startY;
        previousHeight = overlayStack.data.height;
        stacks.push(overlayStack);
      }

      // Add below stack if needed
      const belowStack = createBelowStack(
        imageRows[i],
        imageExtraRows?.[i],
        i,
        previousStartY,
        previousHeight
      );

      if (belowStack) {
        previousStartY = belowStack.startY;
        previousHeight = belowStack.data.height;
        stacks.push(belowStack);
      }

      // Add point data stacks
      const pointStacks = createPointStacks(
        imageRows[i],
        previousStartY,
        previousHeight
      );

      if (pointStacks.length > 0) {
        // Update position tracking for point stacks
        const lastPointStack = pointStacks[pointStacks.length - 1];
        previousStartY = lastPointStack.startY;
        previousHeight = 120;
        stacks.push(...pointStacks);
      }
    }

    return stacks;
  }, [
    imageRows,
    calculateRelativeStartX,
    createImageStack,
    createOverlayStack,
    createBelowStack,
    createPointStacks,
    imageExtraRows,
  ]);

  /**
   * Fetches recovery data for the selected drillhole
   */
  const fetchRecoveries = useCallback(async () => {
    if (isShowSegmentation && selectedDrillhole?.value) {
      try {
        const result = await recoveryRequest.getRecoveryByDrillHole({
          drillHoleId: Number(selectedDrillhole.value),
        });
        if (result.state === RequestState.success && result.data?.items) {
          dispatch(updateRecoveries(result.data.items));
        }
      } catch (error) {
        toast.error("Failed to fetch recoveries");
      }
    }
  }, [isShowSegmentation, selectedDrillhole?.value, dispatch]);
  useEffect(() => {
    fetchRecoveries();
  }, [isShowSegmentation, selectedDrillhole]);

  useEffect(() => {
    if (globalProjectId && selectedDrillhole?.label) {
      requestGetDownholeByProject({
        DepthFrom: image?.depthFrom,
        DepthTo: image?.depthTo,
        DrillHoleName: [selectedDrillhole?.label],
        projectId: globalProjectId,
      });

      dispatch(getGeologySuite({ isActive: true, maxResultCount: 1000 }));
    }
  }, [globalProjectId, selectedDrillhole?.label]);

  return isEmpty(imageRows) ? (
    <div className="flex flex-col items-center justify-center mt-2 w-full ">
      <div className="flex flex-col md:items-center justify-center max-w-md p-8 bg-white rounded-lg shadow-md">
        <img
          src="/images/ic_notResult.png"
          alt="No cropped images"
          className="md:w-40 md:h-40 w-20 h-20 mb-6 opacity-80"
        />
        <h2 className="text-2xl font-bold text-gray-700 mb-2">
          No cropped images available
        </h2>
        <p className="text-gray-500 text-center mb-6">
          There are no cropped images available.
        </p>
        <div className="flex items-center justify-center">
          <div className="w-3 h-3 bg-amber-500 rounded-full animate-ping mr-2"></div>
          <span className="text-amber-500 font-medium">
            Try selecting a different drill hole
          </span>
        </div>
      </div>
    </div>
  ) : (
    <Fragment>
      <div className="">
        <div className="flex flex-col gap-1 mt-1 md:relative">
          <div className="flex items-center justify-between gap-8 w-full pb-1">
            <div className="flex gap-3 items-center">
              <div className="flex items-center gap-3">
                <div className="items-center flex col-span-2">
                  <p className="font-medium col-span-1 min-w-12 w-12">Image</p>
                  <TreeSelect
                    className="min-w-40 col-span-1"
                    placeholder="Select image type"
                    treeData={imageTypeTreeData}
                    value={imageTypeValue}
                    onChange={handleImageTypeChange}
                    loading={isFetchingImageType}
                    allowClear
                    treeDefaultExpandAll
                    dropdownStyle={{
                      maxHeight: 400,
                      overflow: "auto",
                      minWidth: "min(300px, 90vw)", // Responsive: 300px on desktop, 90% viewport width on mobile
                      maxWidth: "min(600px, 95vw)", // Responsive: 600px on desktop, 95% viewport width on mobile
                    }}
                  />
                  {imageTypeValue && (
                    <Select
                      mode="multiple"
                      className="min-w-40 col-span-1"
                      placeholder="View mode"
                      options={viewsOptions}
                      value={viewModes}
                      onChange={onChangeViewModes}
                    />
                  )}
                </div>
                <div className="items-center flex">
                  <p className="font-medium col-span-1">Geophysics</p>
                  <Select
                    className="min-w-40 col-span-2"
                    placeholder="Choose geophysics"
                    options={projectDetail?.geophysicsSuites?.map(
                      (geophysics: any) => ({
                        label: geophysics.name,
                        value: geophysics.id,
                      })
                    )}
                    allowClear
                    value={selectedGeophysic}
                    onChange={handleSelectGeophysics}
                  />
                  {selectedGeophysic && (
                    <Select
                      className="min-w-40 col-span-2"
                      placeholder="Choose attributes"
                      options={geophysicsAttributes.map((attribute) => ({
                        label: attribute.name,
                        value: attribute.name,
                      }))}
                      allowClear
                      mode="multiple"
                      onChange={handleSelectAttributes}
                    />
                  )}
                </div>
                <div className="items-center grid grid-cols-3">
                  <p className="font-medium col-span-1">Rock Line</p>
                  <Select
                    className="min-w-40 col-span-2"
                    placeholder="Select rock line type"
                    options={rockLineOptions}
                    value={selectedRockLineType}
                    onChange={handleRockLineTypeChange}
                    defaultValue={RockLineType.Recovery}
                  />
                </div>
              </div>

              {loggingSuiteMode === "Geotech" && isInterval && (
                <div className="font-medium">
                  From{" "}
                  <Tag color="blue">
                    {measurePointsInterval?.start?.depth.toFixed(2)}{" "}
                  </Tag>
                  to{" "}
                  <Tag color="gold">
                    {measurePointsInterval?.end?.depth.toFixed(2)}
                  </Tag>
                  <Button
                    type="primary"
                    size="small"
                    disabled={
                      !measurePointsInterval?.start &&
                      !measurePointsInterval?.end
                    }
                    onClick={() => {
                      dispatch(updateMeasurePointsInterval({}));
                    }}
                  >
                    Reset
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <LoggingStage
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
          loggingViewStacks={loggingViewStacks}
          onChangeText={onChangeText}
          onEnterChangeText={onEnterChangeText}
          onDblClickText={onDblClickText}
          onChange={onChange}
          directOCRdataRaw={directOCRdataRaw}
          image={image}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          refreshImageData={refreshImageData}
          fetchRecoveries={fetchRecoveries}
          onDeleteOcr={onDeleteOcr}
        />
      </div>
      {isOpenModalOCRList && (
        <ModalOcrList
          OCRdata={OCRdata}
          isOpenModalOCRList={isOpenModalOCRList}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          setOCRdata={setOCRdata}
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
        />
      )}
    </Fragment>
  );
}

export default LoggingImage;
