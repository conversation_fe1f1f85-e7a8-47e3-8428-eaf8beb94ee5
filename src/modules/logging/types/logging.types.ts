import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { ColumnClass } from "@/modules/logging-view/enum/enum";
import { EnumLoggingExtraViews } from "../model/enum/logging.enum";

export interface DisplayColumnConfig {
  fontSize: number;
  color: string;
  strokeWidth: number;
  lineColor: string;
  showText: boolean;
}

export interface Coordinate {
  height: number;
  startY: number;
  endY: number;
}

export interface GeophysicsPoint {
  x: string;
  y: string | number;
}

export interface GeologyDataInfo {
  startX: number;
  width: number;
  name: string;
  columnClass: ColumnClass;
  geologySuiteField?: {
    id: string;
    geologyField?: {
      type: FieldType;
    };
  };
}

export interface DataEntryValue {
  fieldType: FieldType;
  rockType?: {
    code?: string;
    rockStyle?: {
      fillColor?: string;
    };
  };
  description?: string;
  colour?: {
    hexCode?: string;
    name?: string;
  };
  dateValue?: string;
  numberValue?: number;
  number?: {
    unit?: {
      code?: string;
    };
  };
  pickListItem?: {
    name?: string;
  };
  rockNode?: {
    name?: string;
  };
  geologysuiteFieldId?: string;
}

export interface ApiError extends Error {
  response?: {
    data?: {
      error?: {
        message?: string;
      };
    };
  };
}

export interface ModalState {
  isOpen: boolean;
  data?: any;
}

export interface Recovery {
  ocrValueFrom: number;
  ocrValueTo: number;
  length: number;
  recovery: number;
  depthInterval: number;
  fromX: number;
  fromY: number;
  fromImageCropId: number;
  fromRowIndex: number;
  toX: number;
  toY: number;
  toImageCropId: number;
  toRowIndex: number;
  id: number;
  fromOcrId: string;
  toOcrId: string;
}

export interface OCRResultItem {
  id?: string | number;
  rowIndex: number;
  text: string | number;
  x?: number;
  y?: number;
  isEdit?: boolean;
  draftText?: string | number;
}

export enum RockLineType {
  Recovery = 1,
  RQD,
}

export interface RockLine {
  type: RockLineType;
  depthFrom: number;
  depthTo: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  imageCropId: number;
  id: number;
}

/**
 * Represents the coordinate information for an image
 */
export interface ImageCoordinate {
  X: number;
  Y: number;
  Width: number;
  Height: number;
}

/**
 * Represents an image row with all its properties
 */
export interface ImageRow {
  id: number;
  urlCroppedImage: string;
  coordinate: ImageCoordinate;
  depthFrom: number;
  depthTo: number;
  rockLines?: RockLine[];
}

/**
 * Represents an image object with basic properties
 */
export interface ImageData {
  id: number;
  depthFrom: number;
  depthTo: number;
}

/**
 * Represents image type tree node structure
 */
export interface ImageTypeTreeNode {
  title: string;
  value: string;
  key: string;
  children?: ImageTypeTreeNode[];
}

/**
 * Represents the result of type/subtype ID conversion
 */
export interface TypeSubTypeIds {
  typeId: number | undefined;
  subTypeId: number | undefined;
}

/**
 * Props interface for the LoggingImage component
 */
export interface LoggingImageProps {
  imageRows: ImageRow[];
  viewModes: EnumLoggingExtraViews[];
  setViewModes: (modes: EnumLoggingExtraViews[]) => void;
  directOCRdata: OCRResultItem[];
  setDirectOCRdata: (data: OCRResultItem[]) => void;
  image: ImageData;
  directOCRdataRaw: OCRResultItem[];
  getCurrentImage: (
    drillholeId: string,
    skipCount: number,
    imageType?: number,
    imageSubtype?: number
  ) => void;
  setCurrentImage: (skipCount: number) => void;
  selectedImageType?: number;
  selectedImageSubtype?: number;
}
