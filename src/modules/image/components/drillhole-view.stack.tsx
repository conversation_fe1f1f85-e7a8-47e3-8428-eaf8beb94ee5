"use client";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { MultiLoggingInfoInterface } from "@/modules/logging/interface/logging.interface";
import { isEmpty, uniqueId } from "lodash";
import { useMemo, useCallback } from "react";
import {
  reCalculateImageSize,
  resizePointData,
} from "../helpers/image.helpers";
import {
  DrillHoleAttributePoints,
  DrillHoleViewStack,
} from "../model/entities/drillhole.config";
import {
  EnumDrillholeView,
  EnumDrillHoleViewStack,
} from "../model/enum/drillhole.enum";
import { selectDHViewConfig, updateDHViewInfo } from "../redux/imageSlice";
import ImageColumn from "./canvas/image.column";
import PointDataColumn from "./canvas/point-data.column";
import IntervalGeologyColumn from "./canvas/point-data.geology";
import CombinedResultColumn from "./canvas/combined-result.geology";
import { DrillHoleImages } from "./drillhole-view";
import { getCombinedResultText } from "../helpers/point-data.helper";

// Type definitions for better type safety
interface PointData {
  x: number;
  y: number;
}

interface ResizedPointInfo {
  data: PointData[];
  minHeight: number;
  maxHeight: number;
  maxWidth: number;
}

interface ImageInfo {
  list: unknown[];
  minHeight: number;
  maxHeight: number;
  maxWidth: number;
  rotation: number;
}

interface CombinedResultData {
  combineResultText: string;
  depthFrom?: number;
  depthTo?: number;
  dataEntryValues: unknown[];
  drillholeId?: number;
  geologySuiteId?: number;
}

interface HeightBounds {
  minHeight: number;
  maxHeight: number;
}

interface Props {
  dHImages: DrillHoleImages[];
  viewMode: EnumDrillholeView;
  dhAttributePoints: DrillHoleAttributePoints[];
  dhSuitesLogging: MultiLoggingInfoInterface[];
  selectedHoleNames: string[];
}

function DrillHoleViewStacks({
  dHImages,
  viewMode,
  dhAttributePoints,
  dhSuitesLogging,
  selectedHoleNames,
}: Props) {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const showCombinedResultColumn = useAppSelector(
    (state) => state.images.showCombinedResultColumn
  );
  const isCombinedResultTextWide = useAppSelector(
    (state) => state.images.isTextWide
  );

  const { selectedGeologyFieldId } = useAppSelector((state) => state.images);

  const dispatch = useAppDispatch();

  // Memoized helper functions for better performance
  const updateHeightBounds = useCallback(
    (bounds: HeightBounds, newMin: number, newMax: number): HeightBounds => {
      return {
        minHeight: Math.min(bounds.minHeight, newMin),
        maxHeight: Math.max(bounds.maxHeight, newMax),
      };
    },
    []
  );

  const createImageStacks = useCallback(
    (
      holeImages: DrillHoleImages[],
      heightBounds: HeightBounds
    ): { stacks: DrillHoleViewStack[]; bounds: HeightBounds } => {
      const imageStacks = holeImages.map((image, imageIndex) => {
        const imageInfo: ImageInfo = reCalculateImageSize(image, viewMode);

        const stack: DrillHoleViewStack = {
          id: uniqueId(),
          type: EnumDrillHoleViewStack.Image,
          data: imageInfo.list,
          index: imageIndex,
          maxWidth: imageInfo.maxWidth,
          minHeight: imageInfo.minHeight,
          maxHeight: imageInfo.maxHeight,
          startX: dHViewConfigs.startX - 2,
          drillhole: image.drillHole,
          imageType: image.type,
          imageSubType: image.subType,
          typeName: image.typeName,
          subTypeName: image.subTypeName,
          uiProps: {
            rotation: imageInfo.rotation,
          },
        };

        const updatedBounds = updateHeightBounds(
          heightBounds,
          imageInfo.minHeight ?? 0,
          imageInfo.maxHeight ?? 0
        );

        return { stack, bounds: updatedBounds };
      });

      const stacks = imageStacks.map((item) => item.stack);
      const finalBounds = imageStacks.reduce(
        (acc, item) =>
          updateHeightBounds(acc, item.bounds.minHeight, item.bounds.maxHeight),
        heightBounds
      );

      return { stacks, bounds: finalBounds };
    },
    [dHViewConfigs.startX, viewMode, updateHeightBounds]
  );

  const createPointStacks = useCallback(
    (
      holeName: string,
      holeImages: DrillHoleImages[],
      heightBounds: HeightBounds
    ): { stacks: DrillHoleViewStack[]; bounds: HeightBounds } => {
      // Filter points by coordinates
      const filterDhPoints = dhAttributePoints?.filter(
        (point) => point?.coor?.length > 0
      );

      if (filterDhPoints?.length === 0) {
        return { stacks: [], bounds: heightBounds };
      }

      const pointStacks = dhAttributePoints
        .filter((point) => point.drillHoleName === holeName)
        .map((point, index) => {
          const pointInfo: ResizedPointInfo = resizePointData(
            point.coor,
            dHViewConfigs.pointDataWidth
          );

          const stack: DrillHoleViewStack = {
            id: uniqueId(),
            type: EnumDrillHoleViewStack.Point,
            data: pointInfo.data,
            index: index,
            maxWidth: pointInfo.maxWidth,
            minHeight: pointInfo.minHeight,
            maxHeight: pointInfo.maxHeight,
            startX: dHViewConfigs.startX,
            drillhole: holeImages?.[0]?.drillHole,
            extraInfo: point?.type,
          };

          const updatedBounds = updateHeightBounds(
            heightBounds,
            pointInfo.minHeight ?? 0,
            pointInfo.maxHeight ?? 0
          );

          return { stack, bounds: updatedBounds };
        });

      const stacks = pointStacks.map((item) => item.stack);
      const finalBounds = pointStacks.reduce(
        (acc, item) =>
          updateHeightBounds(acc, item.bounds.minHeight, item.bounds.maxHeight),
        heightBounds
      );

      return { stacks, bounds: finalBounds };
    },
    [
      dhAttributePoints,
      dHViewConfigs.startX,
      dHViewConfigs.pointDataWidth,
      updateHeightBounds,
    ]
  );

  const createGeologyStacks = useCallback(
    (
      holeImages: DrillHoleImages[],
      heightBounds: HeightBounds
    ): { stacks: DrillHoleViewStack[]; bounds: HeightBounds } => {
      const loggingEntries = dhSuitesLogging.find(
        (logging) => logging.drillHoleId === holeImages?.[0]?.drillHole?.name
      );

      if (
        isEmpty(dhSuitesLogging) ||
        !loggingEntries ||
        !loggingEntries?.entries?.length ||
        !selectedGeologyFieldId
      ) {
        return { stacks: [], bounds: heightBounds };
      }

      const geologyData = loggingEntries.entries;
      const stackMinHeight = Math.min(
        ...geologyData.map((item) => item.depthFrom ?? 0)
      );
      const stackMaxHeight = Math.max(
        ...geologyData.map((item) => item.depthTo ?? 0)
      );

      const stack: DrillHoleViewStack = {
        id: uniqueId(),
        type: EnumDrillHoleViewStack.Geology,
        data: geologyData,
        index: 0,
        maxWidth: viewMode === EnumDrillholeView.DownHole ? 0.12 : 1,
        minHeight: stackMinHeight,
        maxHeight: stackMaxHeight,
        startX: dHViewConfigs.startX,
        drillhole: holeImages?.[0]?.drillHole,
        extraInfo: loggingEntries.suiteName,
      };

      const updatedBounds = updateHeightBounds(
        heightBounds,
        stackMinHeight,
        stackMaxHeight
      );

      return { stacks: [stack], bounds: updatedBounds };
    },
    [
      dhSuitesLogging,
      selectedGeologyFieldId,
      viewMode,
      dHViewConfigs.startX,
      updateHeightBounds,
    ]
  );

  const createCombinedResultStacks = useCallback(
    (
      holeImages: DrillHoleImages[],
      heightBounds: HeightBounds
    ): { stacks: DrillHoleViewStack[]; bounds: HeightBounds } => {
      const loggingEntries = dhSuitesLogging.find(
        (logging) => logging.drillHoleId === holeImages?.[0]?.drillHole?.name
      );

      if (
        isEmpty(dhSuitesLogging) ||
        !loggingEntries ||
        !loggingEntries?.entries?.length ||
        !showCombinedResultColumn
      ) {
        return { stacks: [], bounds: heightBounds };
      }

      const geologyDatas = loggingEntries.entries;
      const stackMinHeight = Math.min(
        ...geologyDatas.map((item) => item.depthFrom ?? 0)
      );
      const stackMaxHeight = Math.max(
        ...geologyDatas.map((item) => item.depthTo ?? 0)
      );

      const combinedResult: CombinedResultData[] = geologyDatas.map((data) => ({
        combineResultText: getCombinedResultText(data),
        depthFrom: data.depthFrom,
        depthTo: data.depthTo,
        dataEntryValues: data.dataEntryValues,
        drillholeId: data.drillholeId,
        geologySuiteId: data.geologySuiteId,
      }));

      const maxWidth = Math.max(
        ...combinedResult.map((item) => item.combineResultText.length)
      );

      const realMaxWidth =
        viewMode === EnumDrillholeView.DownHole
          ? maxWidth * 0.031
          : maxWidth * 0.155;

      const realMaxWidthDownhole =
        realMaxWidth / 5 < 1.2 ? 1.2 : realMaxWidth / 5;

      const stack: DrillHoleViewStack = {
        id: uniqueId(),
        type: EnumDrillHoleViewStack.GeologyCombinedResult,
        data: combinedResult,
        index: 0,
        maxWidth: isCombinedResultTextWide
          ? realMaxWidth
          : realMaxWidthDownhole,
        minHeight: stackMinHeight,
        maxHeight: stackMaxHeight,
        startX: dHViewConfigs.startX,
        drillhole: holeImages?.[0]?.drillHole,
        extraInfo: loggingEntries.suiteName,
      };

      const updatedBounds = updateHeightBounds(
        heightBounds,
        stackMinHeight,
        stackMaxHeight
      );

      return { stacks: [stack], bounds: updatedBounds };
    },
    [
      dhSuitesLogging,
      showCombinedResultColumn,
      viewMode,
      isCombinedResultTextWide,
      dHViewConfigs.startX,
      updateHeightBounds,
    ]
  );

  const drillHoleStacks: DrillHoleViewStack[] | undefined = useMemo(() => {
    let dHStacks: DrillHoleViewStack[] = [];
    let heightBounds: HeightBounds = {
      minHeight: Infinity,
      maxHeight: -Infinity,
    };

    for (const holeName of selectedHoleNames) {
      // Build stack for each selected hole
      const holeImages = dHImages.filter((image) => {
        return image.drillHole.name === holeName;
      });

      // Create image stacks
      const imageResult = createImageStacks(holeImages, heightBounds);
      dHStacks.push(...imageResult.stacks);
      heightBounds = imageResult.bounds;

      // Create point stacks
      const pointResult = createPointStacks(holeName, holeImages, heightBounds);
      dHStacks.push(...pointResult.stacks);
      heightBounds = pointResult.bounds;

      // Create geology stacks
      const geologyResult = createGeologyStacks(holeImages, heightBounds);
      dHStacks.push(...geologyResult.stacks);
      heightBounds = geologyResult.bounds;

      // Create combined result stacks
      const combinedResult = createCombinedResultStacks(
        holeImages,
        heightBounds
      );
      dHStacks.push(...combinedResult.stacks);
      heightBounds = combinedResult.bounds;
    }

    // recalculate startX
    for (let i = 0; i < dHStacks.length; i++) {
      let startX = 0;
      if (i === 0) {
        startX = dHViewConfigs.startX;
      } else {
        startX =
          (dHStacks?.[i - 1]?.startX ?? 0) +
          (dHStacks[i - 1].maxWidth ?? 0) +
          dHViewConfigs.gap;
      }
      dHStacks[i] = {
        ...dHStacks[i],
        startX: startX - 1,
      };
    }

    // Finalize height bounds
    const finalMinHeight =
      heightBounds.minHeight === Infinity ? 0 : heightBounds.minHeight;
    const finalMaxHeight =
      heightBounds.maxHeight === -Infinity ? 0 : heightBounds.maxHeight;

    dispatch(
      updateDHViewInfo({
        minHeight: finalMinHeight,
        maxHeight: finalMaxHeight,
      })
    );

    return dHStacks;
  }, [
    dHViewConfigs,
    dHImages,
    viewMode,
    dhAttributePoints,
    dhSuitesLogging,
    selectedHoleNames,
    isCombinedResultTextWide,
    showCombinedResultColumn,
    selectedGeologyFieldId,
    createImageStacks,
    createPointStacks,
    createGeologyStacks,
    createCombinedResultStacks,
    dispatch,
  ]);

  const renderStackItem = useCallback(
    (calculatedImage: DrillHoleViewStack) => {
      switch (calculatedImage.type) {
        case EnumDrillHoleViewStack.Image: {
          return (
            <ImageColumn
              key={`${calculatedImage?.type}${calculatedImage?.drillhole?.id}${calculatedImage?.imageType}${calculatedImage?.imageSubType}`}
              images={calculatedImage}
            />
          );
        }
        case EnumDrillHoleViewStack.Point: {
          return (
            <PointDataColumn
              key={calculatedImage?.id}
              dataPoints={calculatedImage}
            />
          );
        }

        case EnumDrillHoleViewStack.Geology: {
          return (
            <IntervalGeologyColumn
              key={calculatedImage?.id}
              intervals={calculatedImage}
            />
          );
        }

        case EnumDrillHoleViewStack.GeologyCombinedResult: {
          return showCombinedResultColumn ? (
            <CombinedResultColumn
              key={calculatedImage?.id}
              intervals={calculatedImage}
            />
          ) : null;
        }

        default:
          return null;
      }
    },
    [showCombinedResultColumn]
  );

  return (drillHoleStacks ?? []).map(renderStackItem);
}

export default DrillHoleViewStacks;
